import Navigation from "@/components/Navigation";
import HeroSection from "@/components/HeroSection";
import RaffleCard from "@/components/RaffleCard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { ArrowRight, TrendingUp } from "lucide-react";

// Featured raffles data
const featuredRaffles = [
  {
    id: "1",
    title: "iPhone 15 Pro Max Giveaway",
    description: "Win the latest iPhone 15 Pro Max 256GB in Space Black. Brand new, unlocked, and ready to use!",
    image: "https://images.unsplash.com/photo-1695048133142-1a20484d2569?w=400&h=300&fit=crop",
    prize: "iPhone 15 Pro Max",
    endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    participantCount: 1247,
    maxParticipants: 5000,
    ticketPrice: 5,
    status: "active" as const,
    views: 15420,
  },
  {
    id: "2",
    title: "Gaming Setup Ultimate",
    description: "Complete gaming setup including RTX 4080 PC, 4K monitor, mechanical keyboard, and gaming chair.",
    image: "https://images.unsplash.com/photo-1593305841991-05c297ba4575?w=400&h=300&fit=crop",
    prize: "$3,500 Gaming Setup",
    endDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
    participantCount: 892,
    ticketPrice: 10,
    status: "active" as const,
    views: 8732,
  },
  {
    id: "3",
    title: "Tesla Model Y Weekend",
    description: "Experience luxury with a Tesla Model Y for an entire weekend, including charging credits.",
    image: "https://images.unsplash.com/photo-1560958089-b8a1929cea89?w=400&h=300&fit=crop",
    prize: "Tesla Model Y Weekend",
    endDate: new Date(Date.now() + 12 * 24 * 60 * 60 * 1000),
    participantCount: 2156,
    maxParticipants: 10000,
    ticketPrice: 25,
    status: "active" as const,
    views: 23451,
  },
];

const Index = () => {
  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <HeroSection />
      
      {/* Featured Raffles Section */}
      <section className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <TrendingUp className="h-6 w-6 text-primary" />
            <h2 className="text-3xl md:text-4xl font-bold">Trending Raffles</h2>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Join these popular raffles before they end. Don't miss your chance to win amazing prizes!
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {featuredRaffles.map((raffle) => (
            <RaffleCard key={raffle.id} {...raffle} />
          ))}
        </div>

        <div className="text-center">
          <Button variant="outline" size="lg" asChild>
            <Link to="/browse">
              View All Raffles
              <ArrowRight className="h-4 w-4 ml-2" />
            </Link>
          </Button>
        </div>
      </section>
    </div>
  );
};

export default Index;
