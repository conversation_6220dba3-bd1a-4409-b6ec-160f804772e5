import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, Users, Trophy, Eye } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface RaffleCardProps {
  id: string;
  title: string;
  description: string;
  image: string;
  prize: string;
  endDate: Date;
  participantCount: number;
  maxParticipants?: number;
  ticketPrice: number;
  status: "active" | "ended" | "upcoming";
  views: number;
}

const RaffleCard = ({
  title,
  description,
  image,
  prize,
  endDate,
  participantCount,
  maxParticipants,
  ticketPrice,
  status,
  views,
}: RaffleCardProps) => {
  const timeLeft = formatDistanceToNow(endDate, { addSuffix: true });
  
  const getStatusBadge = () => {
    switch (status) {
      case "active":
        return <Badge className="bg-success text-success-foreground">Active</Badge>;
      case "ended":
        return <Badge variant="secondary">Ended</Badge>;
      case "upcoming":
        return <Badge className="bg-warning text-warning-foreground">Upcoming</Badge>;
      default:
        return null;
    }
  };

  return (
    <Card className="group hover:shadow-raffle transition-all duration-300 hover:-translate-y-1 bg-gradient-card border-0 overflow-hidden">
      {/* Image Header */}
      <div className="relative overflow-hidden">
        <img
          src={image}
          alt={title}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div className="absolute top-3 left-3">
          {getStatusBadge()}
        </div>
        <div className="absolute top-3 right-3 bg-background/80 backdrop-blur-sm rounded-full px-2 py-1 flex items-center space-x-1">
          <Eye className="h-3 w-3 text-muted-foreground" />
          <span className="text-xs text-muted-foreground">{views}</span>
        </div>
      </div>

      <CardHeader className="pb-3">
        <h3 className="font-bold text-lg group-hover:text-primary transition-colors">
          {title}
        </h3>
        <p className="text-muted-foreground text-sm line-clamp-2">
          {description}
        </p>
      </CardHeader>

      <CardContent className="py-3">
        <div className="space-y-3">
          {/* Prize */}
          <div className="flex items-center space-x-2">
            <Trophy className="h-4 w-4 text-warning" />
            <span className="font-semibold text-foreground">{prize}</span>
          </div>

          {/* Stats Row */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center space-x-1">
              <Clock className="h-4 w-4" />
              <span>{status === "ended" ? "Ended" : timeLeft}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Users className="h-4 w-4" />
              <span>
                {participantCount}
                {maxParticipants && `/${maxParticipants}`}
              </span>
            </div>
          </div>

          {/* Progress Bar (if max participants set) */}
          {maxParticipants && (
            <div className="w-full bg-secondary rounded-full h-2">
              <div
                className="bg-gradient-primary rounded-full h-2 transition-all duration-300"
                style={{
                  width: `${Math.min((participantCount / maxParticipants) * 100, 100)}%`,
                }}
              />
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter className="pt-3">
        <div className="flex items-center justify-between w-full">
          <div className="text-sm">
            <span className="text-muted-foreground">Ticket: </span>
            <span className="font-bold text-primary">
              {ticketPrice === 0 ? "Free" : `$${ticketPrice}`}
            </span>
          </div>
          
          <Button
            variant={status === "active" ? "raffle" : "secondary"}
            size="sm"
            disabled={status === "ended"}
            className="ml-3"
          >
            {status === "ended" ? "View Results" : "Enter Raffle"}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default RaffleCard;