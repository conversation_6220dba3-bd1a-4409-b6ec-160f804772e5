@tailwind base;
@tailwind components;
@tailwind utilities;

/* Raffle Platform Design System - Vibrant and Exciting */

@layer base {
  :root {
    --background: 245 50% 97%;
    --foreground: 240 15% 9%;

    --card: 0 0% 100%;
    --card-foreground: 240 15% 9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 15% 9%;

    --primary: 262 83% 58%;
    --primary-foreground: 0 0% 100%;

    --secondary: 220 30% 95%;
    --secondary-foreground: 240 15% 9%;

    --muted: 220 30% 95%;
    --muted-foreground: 240 10% 50%;

    --accent: 312 73% 70%;
    --accent-foreground: 0 0% 100%;

    --success: 142 71% 45%;
    --success-foreground: 0 0% 100%;

    --warning: 48 96% 53%;
    --warning-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 262 83% 58%;

    /* Raffle-specific design tokens */
    --gradient-primary: linear-gradient(135deg, hsl(262 83% 58%), hsl(312 73% 70%));
    --gradient-success: linear-gradient(135deg, hsl(142 71% 45%), hsl(170 60% 50%));
    --gradient-hero: linear-gradient(135deg, hsl(262 83% 58%) 0%, hsl(312 73% 70%) 50%, hsl(48 96% 53%) 100%);
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%) 0%, hsl(220 30% 98%) 100%);
    
    --shadow-raffle: 0 20px 40px -10px hsl(262 83% 58% / 0.15);
    --shadow-card: 0 10px 30px -5px hsl(240 15% 9% / 0.1);
    --shadow-glow: 0 0 50px hsl(262 83% 58% / 0.3);
    
    --animation-bounce: bounce 2s infinite;
    --animation-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 240 15% 5%;
    --foreground: 0 0% 98%;

    --card: 240 15% 8%;
    --card-foreground: 0 0% 98%;

    --popover: 240 15% 8%;
    --popover-foreground: 0 0% 98%;

    --primary: 262 83% 63%;
    --primary-foreground: 0 0% 100%;

    --secondary: 240 10% 15%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 10% 15%;
    --muted-foreground: 240 5% 65%;

    --accent: 312 73% 75%;
    --accent-foreground: 0 0% 100%;

    --success: 142 71% 50%;
    --success-foreground: 0 0% 100%;

    --warning: 48 96% 58%;
    --warning-foreground: 0 0% 100%;

    --destructive: 0 84% 65%;
    --destructive-foreground: 0 0% 100%;

    --border: 240 10% 20%;
    --input: 240 10% 20%;
    --ring: 262 83% 63%;

    --gradient-primary: linear-gradient(135deg, hsl(262 83% 63%), hsl(312 73% 75%));
    --gradient-success: linear-gradient(135deg, hsl(142 71% 50%), hsl(170 60% 55%));
    --gradient-hero: linear-gradient(135deg, hsl(262 83% 63%) 0%, hsl(312 73% 75%) 50%, hsl(48 96% 58%) 100%);
    --gradient-card: linear-gradient(145deg, hsl(240 15% 8%) 0%, hsl(240 10% 12%) 100%);
    
    --shadow-raffle: 0 20px 40px -10px hsl(262 83% 63% / 0.25);
    --shadow-card: 0 10px 30px -5px hsl(0 0% 0% / 0.3);
    --shadow-glow: 0 0 50px hsl(262 83% 63% / 0.4);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
