import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { Gift, Sparkles, Users, DollarSign } from "lucide-react";
import heroImage from "@/assets/hero-raffle.jpg";

const HeroSection = () => {
  return (
    <section className="relative min-h-[90vh] flex items-center justify-center overflow-hidden">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0 z-0">
        <img
          src={heroImage}
          alt="Raffle hero background"
          className="w-full h-full object-cover opacity-20"
        />
        <div className="absolute inset-0 bg-gradient-hero opacity-10" />
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <Gift className="absolute top-20 left-10 text-primary opacity-20 animate-float h-8 w-8" />
        <Sparkles className="absolute top-32 right-20 text-accent opacity-30 animate-pulse-slow h-6 w-6" />
        <Users className="absolute bottom-32 left-20 text-success opacity-25 animate-bounce-slow h-7 w-7" />
        <DollarSign className="absolute bottom-20 right-10 text-warning opacity-20 animate-float h-8 w-8" />
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center max-w-5xl mx-auto px-4">
        <div className="space-y-8">
          {/* Badge */}
          <div className="inline-flex items-center space-x-2 bg-card/80 backdrop-blur-sm rounded-full px-4 py-2 border">
            <Sparkles className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium">Win Amazing Prizes Today!</span>
          </div>

          {/* Main Heading */}
          <h1 className="text-6xl md:text-7xl lg:text-8xl font-bold">
            <span className="bg-gradient-hero bg-clip-text text-transparent">
              Create & Win
            </span>
            <br />
            <span className="text-foreground">Epic Raffles</span>
          </h1>

          {/* Description */}
          <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Host your own raffles or participate in exciting giveaways. Connect with your community 
            and win incredible prizes in our trusted platform.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-4">
            <Button variant="hero" size="lg" asChild className="text-xl px-8 py-6">
              <Link to="/create">
                <Gift className="h-5 w-5 mr-2" />
                Create Your Raffle
              </Link>
            </Button>
            
            <Button variant="outline" size="lg" asChild className="text-xl px-8 py-6">
              <Link to="/browse">
                Browse Raffles
              </Link>
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 pt-12 max-w-3xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">1,500+</div>
              <div className="text-muted-foreground">Active Raffles</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-success">25,000+</div>
              <div className="text-muted-foreground">Happy Winners</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-accent">$500K+</div>
              <div className="text-muted-foreground">Prizes Won</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;