import { useState } from "react";
import Navigation from "@/components/Navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Upload, Calendar, DollarSign, Users, Gift, AlertCircle } from "lucide-react";
import { toast } from "@/hooks/use-toast";

const CreateRaffle = () => {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    prizeDescription: "",
    ticketPrice: "",
    maxParticipants: "",
    endDate: "",
    endTime: "",
    category: "",
    isPublic: true,
    allowMultipleEntries: false,
  });

  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.title || !formData.description || !formData.prizeDescription) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    // This would normally send data to your backend
    toast({
      title: "Raffle Created Successfully!",
      description: "Your raffle has been created and will be reviewed before going live.",
    });
    
    console.log("Raffle data:", formData);
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <main className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            <span className="bg-gradient-primary bg-clip-text text-transparent">
              Create Your Raffle
            </span>
          </h1>
          <p className="text-xl text-muted-foreground">
            Set up your raffle and start connecting with your community.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Gift className="h-5 w-5 text-primary" />
                <span>Basic Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="title">Raffle Title *</Label>
                <Input
                  id="title"
                  placeholder="iPhone 15 Pro Max Giveaway"
                  value={formData.title}
                  onChange={(e) => handleInputChange("title", e.target.value)}
                  className="mt-2"
                />
              </div>

              <div>
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  placeholder="Describe your raffle, rules, and what makes it special..."
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  className="mt-2 min-h-[120px]"
                />
              </div>

              <div>
                <Label htmlFor="prize">Prize Description *</Label>
                <Input
                  id="prize"
                  placeholder="iPhone 15 Pro Max 256GB Space Black"
                  value={formData.prizeDescription}
                  onChange={(e) => handleInputChange("prizeDescription", e.target.value)}
                  className="mt-2"
                />
              </div>

              <div>
                <Label htmlFor="category">Category</Label>
                <Select value={formData.category} onValueChange={(value) => handleInputChange("category", value)}>
                  <SelectTrigger className="mt-2">
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="electronics">Electronics</SelectItem>
                    <SelectItem value="gaming">Gaming</SelectItem>
                    <SelectItem value="automotive">Automotive</SelectItem>
                    <SelectItem value="travel">Travel</SelectItem>
                    <SelectItem value="cash">Cash Prizes</SelectItem>
                    <SelectItem value="lifestyle">Lifestyle</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Prize Image */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Upload className="h-5 w-5 text-primary" />
                <span>Prize Image</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border-2 border-dashed border-border rounded-lg p-8 text-center">
                {imagePreview ? (
                  <div className="space-y-4">
                    <img
                      src={imagePreview}
                      alt="Prize preview"
                      className="max-w-full h-48 object-cover mx-auto rounded-lg"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setImagePreview(null)}
                    >
                      Remove Image
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <Upload className="h-12 w-12 text-muted-foreground mx-auto" />
                    <div>
                      <p className="text-muted-foreground">Upload an image of your prize</p>
                      <p className="text-sm text-muted-foreground">PNG, JPG up to 10MB</p>
                    </div>
                    <Input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="max-w-xs mx-auto"
                    />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Raffle Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="h-5 w-5 text-primary" />
                <span>Raffle Settings</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="ticketPrice">Ticket Price ($)</Label>
                  <Input
                    id="ticketPrice"
                    type="number"
                    min="0"
                    step="0.01"
                    placeholder="5.00"
                    value={formData.ticketPrice}
                    onChange={(e) => handleInputChange("ticketPrice", e.target.value)}
                    className="mt-2"
                  />
                  <p className="text-sm text-muted-foreground mt-1">Set to 0 for free entry</p>
                </div>

                <div>
                  <Label htmlFor="maxParticipants">Max Participants (Optional)</Label>
                  <Input
                    id="maxParticipants"
                    type="number"
                    min="1"
                    placeholder="1000"
                    value={formData.maxParticipants}
                    onChange={(e) => handleInputChange("maxParticipants", e.target.value)}
                    className="mt-2"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="endDate">End Date</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => handleInputChange("endDate", e.target.value)}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label htmlFor="endTime">End Time</Label>
                  <Input
                    id="endTime"
                    type="time"
                    value={formData.endTime}
                    onChange={(e) => handleInputChange("endTime", e.target.value)}
                    className="mt-2"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="isPublic">Public Raffle</Label>
                    <p className="text-sm text-muted-foreground">Allow anyone to discover and join your raffle</p>
                  </div>
                  <Switch
                    id="isPublic"
                    checked={formData.isPublic}
                    onCheckedChange={(checked) => handleInputChange("isPublic", checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="allowMultiple">Allow Multiple Entries</Label>
                    <p className="text-sm text-muted-foreground">Let participants buy multiple tickets</p>
                  </div>
                  <Switch
                    id="allowMultiple"
                    checked={formData.allowMultipleEntries}
                    onCheckedChange={(checked) => handleInputChange("allowMultipleEntries", checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Legal Notice */}
          <Card className="border-warning/20 bg-warning/5">
            <CardContent className="pt-6">
              <div className="flex items-start space-x-3">
                <AlertCircle className="h-5 w-5 text-warning mt-0.5" />
                <div className="text-sm text-warning-foreground">
                  <p className="font-medium mb-2">Important Legal Notice</p>
                  <p>
                    By creating this raffle, you confirm that you own or have the right to give away the prize, 
                    comply with all local laws and regulations regarding raffles and giveaways, and agree to 
                    our terms of service. Some jurisdictions may require permits or have restrictions on raffles.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-center pt-6">
            <Button type="submit" variant="hero" size="lg" className="px-12">
              Create Raffle
            </Button>
          </div>
        </form>
      </main>
    </div>
  );
};

export default CreateRaffle;