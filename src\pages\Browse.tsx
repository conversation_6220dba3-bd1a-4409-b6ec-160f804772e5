import { useState } from "react";
import Navigation from "@/components/Navigation";
import RaffleCard from "@/components/RaffleCard";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Filter } from "lucide-react";

// Mock data for demonstration
const mockRaffles = [
  {
    id: "1",
    title: "iPhone 15 Pro Max Giveaway",
    description: "Win the latest iPhone 15 Pro Max 256GB in Space Black. Brand new, unlocked, and ready to use!",
    image: "https://images.unsplash.com/photo-1695048133142-1a20484d2569?w=400&h=300&fit=crop",
    prize: "iPhone 15 Pro Max",
    endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    participantCount: 1247,
    maxParticipants: 5000,
    ticketPrice: 5,
    status: "active" as const,
    views: 15420,
  },
  {
    id: "2",
    title: "Gaming Setup Ultimate",
    description: "Complete gaming setup including RTX 4080 PC, 4K monitor, mechanical keyboard, and gaming chair.",
    image: "https://images.unsplash.com/photo-1593305841991-05c297ba4575?w=400&h=300&fit=crop",
    prize: "$3,500 Gaming Setup",
    endDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
    participantCount: 892,
    ticketPrice: 10,
    status: "active" as const,
    views: 8732,
  },
  {
    id: "3",
    title: "Tesla Model Y Weekend",
    description: "Experience luxury with a Tesla Model Y for an entire weekend, including charging credits.",
    image: "https://images.unsplash.com/photo-1560958089-b8a1929cea89?w=400&h=300&fit=crop",
    prize: "Tesla Model Y Weekend",
    endDate: new Date(Date.now() + 12 * 24 * 60 * 60 * 1000),
    participantCount: 2156,
    maxParticipants: 10000,
    ticketPrice: 25,
    status: "active" as const,
    views: 23451,
  },
  {
    id: "4",
    title: "MacBook Pro M3 Creator Kit",
    description: "Perfect for content creators: MacBook Pro M3, iPad Pro, AirPods Pro, and creative software bundle.",
    image: "https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=400&h=300&fit=crop",
    prize: "MacBook Pro M3 + Accessories",
    endDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    participantCount: 3421,
    maxParticipants: 5000,
    ticketPrice: 15,
    status: "ended" as const,
    views: 34521,
  },
  {
    id: "5",
    title: "Dream Vacation to Bali",
    description: "7-day all-inclusive vacation to Bali for 2 people including flights, luxury resort, and activities.",
    image: "https://images.unsplash.com/photo-1537953773345-d172ccf13cf1?w=400&h=300&fit=crop",
    prize: "Bali Vacation for 2",
    endDate: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000),
    participantCount: 567,
    ticketPrice: 50,
    status: "upcoming" as const,
    views: 4923,
  },
  {
    id: "6",
    title: "Cash Prize $10,000",
    description: "Straight cash prize of $10,000 - no strings attached. Use it however you want!",
    image: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=300&fit=crop",
    prize: "$10,000 Cash",
    endDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000),
    participantCount: 4532,
    maxParticipants: 20000,
    ticketPrice: 20,
    status: "active" as const,
    views: 67834,
  },
];

const Browse = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [sortBy, setSortBy] = useState("newest");

  const filteredRaffles = mockRaffles.filter((raffle) => {
    const matchesSearch = raffle.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         raffle.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === "all" || raffle.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            <span className="bg-gradient-primary bg-clip-text text-transparent">
              Browse Raffles
            </span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Discover amazing raffles and win incredible prizes from trusted creators worldwide.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-card rounded-lg border p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search raffles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Status Filter */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="upcoming">Upcoming</SelectItem>
                <SelectItem value="ended">Ended</SelectItem>
              </SelectContent>
            </Select>

            {/* Sort */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">Newest</SelectItem>
                <SelectItem value="ending">Ending Soon</SelectItem>
                <SelectItem value="popular">Most Popular</SelectItem>
                <SelectItem value="prize">Prize Value</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" size="icon" className="md:w-auto">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-muted-foreground">
            Showing {filteredRaffles.length} of {mockRaffles.length} raffles
          </p>
        </div>

        {/* Raffle Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {filteredRaffles.map((raffle) => (
            <RaffleCard key={raffle.id} {...raffle} />
          ))}
        </div>

        {/* Load More */}
        <div className="text-center">
          <Button variant="outline" size="lg">
            Load More Raffles
          </Button>
        </div>
      </main>
    </div>
  );
};

export default Browse;