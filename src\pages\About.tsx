import Navigation from "@/components/Navigation";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Shield, Users, Zap, Trophy, Heart, Sparkles } from "lucide-react";
import { Link } from "react-router-dom";

const About = () => {
  const features = [
    {
      icon: Shield,
      title: "Secure & Trusted",
      description: "All raffles are verified and payments are secured through encrypted transactions.",
    },
    {
      icon: Users,
      title: "Community Driven",
      description: "Connect with creators and participants from around the world in a vibrant community.",
    },
    {
      icon: Zap,
      title: "Instant Setup",
      description: "Create and launch your raffle in minutes with our intuitive creation tools.",
    },
    {
      icon: Trophy,
      title: "Fair & Transparent",
      description: "Our provably fair system ensures every participant has an equal chance to win.",
    },
  ];

  const steps = [
    {
      step: 1,
      title: "Create Your Account",
      description: "Sign up for free and verify your identity to start creating raffles.",
    },
    {
      step: 2,
      title: "Set Up Your Raffle",
      description: "Add your prize details, set ticket prices, and customize your raffle settings.",
    },
    {
      step: 3,
      title: "Share & Promote",
      description: "Get a unique link to share with your audience across social media and beyond.",
    },
    {
      step: 4,
      title: "Draw Winner",
      description: "When your raffle ends, our system automatically selects a winner fairly.",
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <section className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            How <span className="bg-gradient-primary bg-clip-text text-transparent">RaffleWin</span> Works
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            The easiest way to create engaging raffles and giveaways. Whether you're a content creator, 
            business owner, or just want to spread some joy, we've got you covered.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="raffle" size="lg" asChild>
              <Link to="/create">Start Creating</Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link to="/browse">Browse Raffles</Link>
            </Button>
          </div>
        </section>

        {/* Features Grid */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">Why Choose RaffleWin?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <Card key={index} className="text-center group hover:shadow-raffle transition-all duration-300">
                <CardHeader>
                  <div className="mx-auto w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                    <feature.icon className="h-8 w-8 text-primary-foreground" />
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* How It Works */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">Getting Started is Easy</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {steps.map((step, index) => (
              <div key={index} className="text-center">
                <div className="relative mb-6">
                  <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto text-2xl font-bold text-primary-foreground">
                    {step.step}
                  </div>
                  {index < steps.length - 1 && (
                    <div className="hidden lg:block absolute top-8 left-full w-full h-0.5 bg-gradient-primary opacity-30 transform -translate-y-1/2" />
                  )}
                </div>
                <h3 className="text-xl font-semibold mb-3">{step.title}</h3>
                <p className="text-muted-foreground">{step.description}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Stats Section */}
        <section className="bg-gradient-card rounded-2xl p-8 mb-16 border">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4">Join Thousands of Happy Users</h2>
            <p className="text-muted-foreground">Our community is growing every day with amazing creators and participants.</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-primary mb-2">50K+</div>
              <div className="text-muted-foreground">Total Users</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-success mb-2">1.5K+</div>
              <div className="text-muted-foreground">Active Raffles</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-accent mb-2">25K+</div>
              <div className="text-muted-foreground">Happy Winners</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-warning mb-2">$500K+</div>
              <div className="text-muted-foreground">Prizes Distributed</div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="text-center bg-gradient-hero rounded-2xl p-12 text-white">
          <Sparkles className="h-16 w-16 mx-auto mb-6 animate-pulse-slow" />
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Create Your First Raffle?</h2>
          <p className="text-xl mb-8 opacity-90">
            Join our community and start connecting with your audience through exciting raffles and giveaways.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="secondary" size="lg" asChild className="bg-white text-primary hover:bg-white/90">
              <Link to="/create">
                <Heart className="h-5 w-5 mr-2" />
                Create Your Raffle
              </Link>
            </Button>
            <Button variant="outline" size="lg" className="border-white text-white hover:bg-white/10">
              <Link to="/browse">Explore Raffles</Link>
            </Button>
          </div>
        </section>
      </main>
    </div>
  );
};

export default About;